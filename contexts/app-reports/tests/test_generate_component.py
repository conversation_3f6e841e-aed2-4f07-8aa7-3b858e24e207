#!/usr/bin/env python3
"""
Simple validation test for Real Estate Pydantic models.
"""

from models.lender import (
    RealEstateReport,
    Scorecard,
    Summary,
    Rating,
    Scores,
    Characteristics,
    Money,
    LegalStatus,
    ScorecardType,
    AreaUnit,
    TargetType,
    TopicCode,
    YesNo,
    PortfolioImprovementTarget,
    IntensitySection
)

def test_basic_models():
    """Test basic model creation and validation."""
    print("Testing basic models...")
    
    # Test Money model
    try:
        money = Money(amount=1000.50, currency="USD")
        assert money.amount == 1000.50
        assert money.currency == "USD"
        print("✓ Money model validation passed")
    except Exception as e:
        print(f"✗ Money model validation failed: {e}")
        return False
    
    # Test invalid Money (should fail)
    try:
        invalid_money = Money(amount=-100, currency="USD")
        print("✗ Money validation should have failed for negative amount")
        return False
    except Exception:
        print("✓ Money model correctly rejected negative amount")
    
    # Test Scores model
    try:
        scores = Scores(gresb_score=85.5, management_score=90.0)
        assert scores.gresb_score == 85.5
        print("✓ Scores model validation passed")
    except Exception as e:
        print(f"✗ Scores model validation failed: {e}")
        return False
    
    # Test invalid Scores (should fail)
    try:
        invalid_scores = Scores(gresb_score=150.0)
        print("✗ Scores validation should have failed for score > 100")
        return False
    except Exception:
        print("✓ Scores model correctly rejected score > 100")
    
    return True

def test_characteristics():
    """Test Characteristics model validation."""
    print("\nTesting characteristics...")
    
    # Test valid characteristics for Non-listed (requires strategy)
    try:
        chars = Characteristics(
            strategy="Core",
            legal_status="Non-listed",
            property_type="Office",
            location="Europe"
        )
        assert chars.strategy == "Core"
        print("✓ Characteristics model validation passed (Non-listed with strategy)")
    except Exception as e:
        print(f"✗ Characteristics model validation failed: {e}")
        return False
    
    # Test invalid: Non-listed without strategy (should fail)
    try:
        invalid_chars = Characteristics(
            legal_status="Non-listed",
            property_type="Office",
            location="Europe"
        )
        print("✗ Characteristics validation should have failed (Non-listed without strategy)")
        return False
    except Exception:
        print("✓ Characteristics model correctly rejected Non-listed without strategy")
    
    # Test valid: Listed without strategy
    try:
        chars_listed = Characteristics(
            legal_status="Listed",
            property_type="Office",
            location="Europe"
        )
        assert chars_listed.strategy is None
        print("✓ Characteristics model validation passed (Listed without strategy)")
    except Exception as e:
        print(f"✗ Characteristics model validation failed: {e}")
        return False
    
    return True

def test_targets():
    """Test target models."""
    print("\nTesting targets...")
    
    try:
        target = PortfolioImprovementTarget(
            topic_code=TopicCode.EN,
            topic="Energy consumption",
            type=TargetType.ABSOLUTE,
            long_term_target=25.0,
            baseline_year=2020,
            end_year=2030,
            externally_communicated=YesNo.YES
        )
        assert target.topic_code == "EN"
        assert target.baseline_year == 2020
        print("✓ PortfolioImprovementTarget validation passed")
    except Exception as e:
        print(f"✗ PortfolioImprovementTarget validation failed: {e}")
        return False
    
    # Test invalid baseline year (should fail)
    try:
        invalid_target = PortfolioImprovementTarget(
            topic_code=TopicCode.EN,
            topic="Energy",
            type=TargetType.ABSOLUTE,
            long_term_target=25.0,
            baseline_year=1999,  # < 2000
            end_year=2030,
            externally_communicated=YesNo.YES
        )
        print("✗ Target validation should have failed for baseline_year < 2000")
        return False
    except Exception:
        print("✓ PortfolioImprovementTarget correctly rejected baseline_year < 2000")
    
    return True

def test_intensity_section():
    """Test IntensitySection model."""
    print("\nTesting intensity section...")
    
    try:
        intensity = IntensitySection(
            covered_assets=50,
            covered_area=25000.0,
            covered_area_pct=95.0,
            vacancy_rate=5.5,
            intensity=180.5,
            intensity_unit="kWh/(m<sup>2</sup>)",
            area_unit=AreaUnit.SQUARE_METERS
        )
        assert intensity.covered_assets == 50
        assert intensity.vacancy_rate == 5.5
        print("✓ IntensitySection validation passed")
    except Exception as e:
        print(f"✗ IntensitySection validation failed: {e}")
        return False
    
    # Test invalid covered_area_pct (should fail)
    try:
        invalid_intensity = IntensitySection(
            covered_assets=50,
            covered_area=25000.0,
            covered_area_pct=150.0,  # > 100
            intensity_unit="kWh/(m<sup>2</sup>)"
        )
        print("✗ IntensitySection validation should have failed for covered_area_pct > 100")
        return False
    except Exception:
        print("✓ IntensitySection correctly rejected covered_area_pct > 100")
    
    return True

def test_complete_report():
    """Test creating a complete minimal report."""
    print("\nTesting complete report...")
    
    try:
        # Create basic components
        rating = Rating(rating="4 Star", stars=4)
        scores = Scores(gresb_score=85.5, management_score=90.0)
        characteristics = Characteristics(
            strategy="Core",
            legal_status="Non-listed",
            property_type="Office",
            location="Europe"
        )
        
        summary = Summary(
            rating=rating,
            scores=scores,
            characteristics=characteristics
        )
        
        scorecard = Scorecard(summary=summary)
        
        report = RealEstateReport(
            scorecards={
                ScorecardType.STANDING_INVESTMENT: scorecard
            }
        )
        
        assert len(report.scorecards) == 1
        assert ScorecardType.STANDING_INVESTMENT in report.scorecards
        print("✓ Complete report validation passed")
        print(f"  Report contains {len(report.scorecards)} scorecard(s)")
        
        # Test serialization
        report_dict = report.model_dump()
        assert isinstance(report_dict, dict)
        assert "scorecards" in report_dict
        print("✓ Report serialization passed")
        
    except Exception as e:
        print(f"✗ Complete report validation failed: {e}")
        return False
    
    return True

def test_enums():
    """Test enum values."""
    print("\nTesting enums...")
    
    try:
        assert LegalStatus.LISTED == "Listed"
        assert LegalStatus.NON_LISTED == "Non-listed"
        assert ScorecardType.STANDING_INVESTMENT == "standing_investment"
        assert AreaUnit.SQUARE_METERS == "m<sup>2</sup>"
        assert TargetType.ABSOLUTE == "Absolute"
        assert TopicCode.EN == "EN"
        assert YesNo.YES == "Yes"
        print("✓ All enum values are correct")
    except Exception as e:
        print(f"✗ Enum validation failed: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("=" * 50)
    print("Running Real Estate Model Validation Tests")
    print("=" * 50)
    
    tests = [
        test_basic_models,
        test_characteristics,
        test_targets,
        test_intensity_section,
        test_complete_report,
        test_enums
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with error: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 All tests passed! The Pydantic models are working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the model definitions.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 