from models.lender import LenderReport, ScorecardType, Scorecard, Summary
import polars as pl


class LenderReportGenerator:
    """
    This class is used to generate a lender report.
    There are two ideas.

    Design goal: 
    1. pre compute data before parsing
    1. avoid any in model computation or renaming.

    2. generate each component and construct in the end
    2. generate the entire report at once like in R but create separate data layer in between for easier export and modifiability.
        - when updating the report, we spend most time changing the report structure.

    ----------------------------
    Data grabbing or mapping to which component is needed for both.


    """
    def __init__(self, report: LenderReport):
        self.report = report

    
    def generate(self):
        return self.report

from models.lender_report import Rating, Scores, Characteristics

class LenderSummary:
    """
    This class is used to generate a lender scorecard.
    """
    def __init__(self, peer_groups: pl.DataFrame, responses: pl.DataFrame, response_id: str):
        self.section : Summary = Summary()
        self.response_id = response_id
        self.peer_groups = peer_groups
        self.responses = responses

    def get_rating(self):
        rating_var = "GRESB_RATING"

        stars = self.responses.filter(pl.col("response_id") == self.response_id).select(rating_var).item()
        rating = f"{stars} Star"

        return Rating(rating="4 Star", stars=4)

    def get_scores(self):
        gresb_score_var = "GRESB_SCORE"
        management_score_var = "MANAGEMENT_SCORE"
        gresb_score = self.responses.filter(pl.col("response_id") == self.response_id).select(gresb_score_var).item()
        management_score = self.responses.filter(pl.col("response_id") == self.response_id).select(management_score_var).item()
        return Scores(gresb_score=gresb_score, management_score=management_score)

    def get_characteristics(self):
        return Characteristics(
            strategy="Core",
            legal_status="Listed",
            property_type="Office",
            location="New York",
        )

    def generate_section(self):
        return Summary(
            rating=self.get_rating(),
            scores=self.get_scores(),
            characteristics=self.get_characteristics(),
        )


from models.lender_data import SummaryData
class LenderSummaryMk2:
    """
    This class is used to generate a lender scorecard.
    """
    def __init__(self, peer_groups: pl.DataFrame, responses: pl.DataFrame, response_id: str):
        self.section : Summary = Summary()
        self.response_id = response_id
        self.peer_groups = peer_groups
        self.responses = responses
        self.summary_data = SummaryData(responses=responses, peer_groups=peer_groups)

    def get_data_summary_data(self):
        return self.summary_data

    def generate_section(self):
        data = self.get_data_summary_data()

        return Summary(
            rating=Rating(rating=data.rating, stars=data.stars),
            scores=Scores(gresb_score=data.gresb_score, management_score=data.management_score),
            characteristics=Characteristics(
                strategy=data.strategy,
                legal_status=data.legal_status,
                property_type=data.property_type,
                location=data.location,
            ),
        )

if __name__ == "__main__":
    print("Hello, World!")