from typing import Optional, Dict, List, Union, Any
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum
from datetime import datetime


class LegalStatus(str, Enum):
    LISTED = "Listed"
    NON_LISTED = "Non-listed"


class ScorecardType(str, Enum):
    STANDING_INVESTMENT = "standing_investment"
    DEVELOPMENT = "development"
    MANAGEMENT_ONLY = "management_only"
    PERFORMANCE_ONLY = "performance_only"
    DEVELOPMENT_ONLY = "development_only"


class AreaUnit(str, Enum):
    SQUARE_METERS = "m<sup>2</sup>"
    SQUARE_FEET = "sq. ft."


class TargetType(str, Enum):
    ABSOLUTE = "Absolute"
    LIKE_FOR_LIKE = "Like-for-like"
    INTENSITY_BASED = "Intensity-based"
    NO_TARGET = "No target"


class TopicCode(str, Enum):
    EN = "EN"
    REN = "REN"
    GHG = "GHG"
    WAT = "WAT"
    WAS = "WAS"
    BC = "BC"
    DC = "DC"
    OTHER = "OTHER"


class YesNo(str, Enum):
    YES = "Yes"
    NO = "No"


class CertificationQuestion(str, Enum):
    DESIGN_CERTIFICATIONS = "design_certifications"
    OPERATIONAL_CERTIFICATIONS = "operational_certifications"
    ENERGY_RATINGS = "energy_ratings"


class CertificationHeader(str, Enum):
    BRAND = "brand"
    SCHEME = "scheme"
    AREA_P = "area_p"
    AST_COUNT = "ast_count"
    BENCH_AREA_P = "bench_area_p"


# Common/Base Models
class Money(BaseModel):
    amount: float = Field(..., ge=0)
    currency: str = Field(..., min_length=3, max_length=3)


class Percent(BaseModel):
    value: float = Field(..., ge=0, le=100)
    
    @field_validator('value')
    def validate_percent(cls, v):
        return round(v, 2)


class ReportingPeriod(BaseModel):
    start_date: str
    end_date: str


class Rating(BaseModel):
    rating: Optional[str] = None
    stars: Optional[int] = Field(None, ge=1, le=5)


class Scores(BaseModel):
    gresb_score: Optional[float] = Field(None, ge=0, le=100)
    management_score: Optional[float] = Field(None, ge=0, le=100)
    performance_score: Optional[float] = Field(None, ge=0, le=100)
    development_score: Optional[float] = Field(None, ge=0, le=100)


# Entity and Characteristics Models
class Characteristics(BaseModel):
    strategy: Optional[str] = None
    legal_status: str
    property_type: str
    location: str

    @model_validator
    def validate_strategy_required_for_non_listed(cls, values):
        if values.get('legal_status') == 'Non-listed' and not values.get('strategy'):
            raise ValueError('strategy is required for Non-listed legal status')
        return values


class GroupCharacteristics(BaseModel):
    strategy: Optional[Union[str, List[str]]] = None
    property_type: Optional[Union[str, List[Union[str, List[str]]]]] = None
    location: Optional[Union[str, List[Union[str, List[str], None]]]] = None
    tenant_controlled: Optional[Union[bool, str]] = None
    commencement_date: Optional[str] = None
    asset_count: Optional[str] = None
    size_in_gav: Optional[str] = None


class PeerGroupRank(BaseModel):
    rank: int
    size: int
    group_characteristics: GroupCharacteristics


class CustomizedPeerGroupRank(BaseModel):
    rank: int
    size: int
    modified_selection: Optional[bool] = None
    group_characteristics: GroupCharacteristics


# Target Models
class PortfolioImprovementTarget(BaseModel):
    topic_code: TopicCode
    topic: str = Field(..., min_length=2)
    type: TargetType
    long_term_target: float
    baseline_year: int = Field(..., ge=2000)
    end_year: int = Field(..., ge=2020)
    externally_communicated: YesNo


class NetZeroTarget(BaseModel):
    target_scope: str
    embodied_carbon_included: YesNo
    baseline_year: int = Field(..., ge=2000)
    interim_year: Union[str, None]
    interim_target_percent: Optional[float] = None
    end_year: Union[str, None]
    percent_portfolio_covered: float = Field(..., ge=0, le=100)
    net_zero_aligned: Optional[str] = None
    science_based: YesNo
    third_party_validated: YesNo
    publicly_communicated: YesNo


class PortfolioImprovementTargets(BaseModel):
    points: float = Field(..., ge=0)
    max_points: float = Field(..., ge=0)
    targets: List[PortfolioImprovementTarget]
    methodology: Optional[str] = None


class NetZeroTargets(BaseModel):
    points: float = Field(..., ge=0)
    max_points: float = Field(..., ge=0)
    targets: List[NetZeroTarget]
    methodology: Optional[str] = None


class Targets(BaseModel):
    portfolio_improvement_targets: PortfolioImprovementTargets
    net_zero_targets: NetZeroTargets


# Performance and Intensity Models
class IntensitySection(BaseModel):
    covered_assets: int = Field(..., ge=0)
    covered_area: float = Field(..., ge=0)
    covered_area_pct: float = Field(..., ge=0, le=100)
    vacancy_rate: Optional[float] = Field(None, ge=0, le=100)
    intensity: Optional[float] = Field(None, ge=0)
    intensity_unit: str = Field(..., min_length=1)
    area_unit: Optional[AreaUnit] = None


class DataCoverage(BaseModel):
    entity: Optional[Union[float, int]] = None
    benchmark: Optional[Union[float, int]] = None


class ScopeDataCoverage(BaseModel):
    landlord: Optional[DataCoverage] = None
    tenant: Optional[DataCoverage] = None
    scope_1_2: Optional[DataCoverage] = None
    scope_3: Optional[DataCoverage] = None


class LFLPerformanceItem(BaseModel):
    entity: Optional[Union[float, List[float]]] = None
    benchmark: Optional[Union[float, List[float]]] = None
    coverage: float = Field(..., ge=0, le=100)


class LFLPerformance(BaseModel):
    landlord: Optional[LFLPerformanceItem] = None
    tenant: Optional[LFLPerformanceItem] = None
    scope_1_2: Optional[LFLPerformanceItem] = None
    scope_3: Optional[LFLPerformanceItem] = None
    total: LFLPerformanceItem


class SustainableConsumptionYear(BaseModel):
    entity: Optional[float] = Field(None, ge=0, le=100)
    benchmark: Optional[float] = Field(None, ge=0, le=100)
    year: int = Field(..., ge=2019)


class SustainableConsumptionPerc(BaseModel):
    current_year: SustainableConsumptionYear
    last_year: SustainableConsumptionYear


class SustainableConsumptionComposition(BaseModel):
    entity: Dict[str, Optional[float]]
    benchmark: Dict[str, Optional[float]]


class SustainableConsumption(BaseModel):
    perc: SustainableConsumptionPerc
    composition: SustainableConsumptionComposition


# this one from R1 table
class PortfolioCharacteristics(BaseModel):
    asset_count: int = Field(..., ge=0)
    asset_area: Union[int, float] = Field(..., ge=0)
    p_area_landlord: Optional[float] = Field(None, ge=0, le=100)
    p_area_tenant: Optional[float] = Field(None, ge=0, le=100)
    p_area_scope_1_2: Optional[float] = Field(None, ge=0, le=100)
    p_area_scope_3: Optional[float] = Field(None, ge=0, le=100)

# from portfolio level aggregation
class Overview(BaseModel):
    data_year: int = Field(..., ge=2017)
    data_coverage: float = Field(..., ge=0, le=100)
    consumption: Optional[Union[float, List[float]]] = None
    sustainable_consumption: Optional[Union[float, List[float]]] = None
    offset: Optional[Union[float, List[float]]] = None
    scope_1: Optional[Union[float, List[float]]] = Field(None, ge=0)
    scope_2_location: Optional[Union[float, List[float]]] = Field(None, ge=0)
    scope_2_market: Optional[Union[float, List[float]]] = Field(None, ge=0)
    scope_3: Optional[Union[float, List[float]]] = Field(None, ge=0)
    additional_info: Optional[str] = None
    non_operational_consumption: Optional[float] = None


class EnergyEfficiency(BaseModel):
    covered_assets: Optional[int] = Field(None, ge=0)
    covered_area_pct: Optional[float] = Field(None, ge=0, le=100)


# Country Level Models
class CountryData(BaseModel):
    country: str = Field(..., min_length=2)
    gav_percentage: float = Field(..., ge=0, le=100)
    portfolio_characteristics: Dict[str, PortfolioCharacteristics]
    overview: Overview
    data_cov: ScopeDataCoverage
    intensity_section: IntensitySection
    lfl_performance: LFLPerformance
    energy_efficiency: Optional[EnergyEfficiency] = None
    sustainable_consumption: Optional[SustainableConsumption] = None


class PropertyTypeSection(BaseModel):
    property_type: str = Field(..., min_length=2)
    gav_percentage: float = Field(..., ge=0, le=100)
    countries: List[CountryData] = Field(..., min_items=1)


class ResourceSection(BaseModel):
    area_unit: AreaUnit
    sections: List[PropertyTypeSection] = Field(..., min_items=1)


# Certification Models
class CertificationRow(BaseModel):
    brand: str
    scheme: Optional[str] = None
    area_p: float = Field(..., ge=0, le=100)
    ast_count: Optional[int] = Field(None, ge=0)
    bench_area_p: Optional[float] = Field(None, ge=0, le=100)


class CertificationAnswers(BaseModel):
    headers: Optional[List[CertificationHeader]] = Field(None, min_items=4)
    rows: List[CertificationRow]


class CertificationTable(BaseModel):
    question: CertificationQuestion
    question_type: str = Field("table_certifications")
    answers: CertificationAnswers


class CertificationTables(BaseModel):
    __root__: List[CertificationTable] = Field(..., min_items=3, max_items=3)


# CRREM Models
class DecarbonizationPathwayPoint(BaseModel):
    year: int = Field(..., ge=2015)
    intensity: float = Field(..., ge=0)


class AssetCount(BaseModel):
    covered: int = Field(..., ge=0)
    no_coverage: int = Field(..., ge=0)
    no_path: int = Field(..., ge=0)


class FloorAreaPercent(BaseModel):
    covered: int = Field(..., ge=0, le=100)
    no_coverage: int = Field(..., ge=0, le=100)
    no_path: int = Field(..., ge=0, le=100)


class Eligibility(BaseModel):
    asset_count: AssetCount
    floor_area_percent: FloorAreaPercent


class Stranded(BaseModel):
    floor_area_percent: Optional[int] = Field(None, ge=0, le=100)
    asset_count: Optional[int] = Field(None, ge=0)
    average_year: Optional[str] = None


class CRREMSection(BaseModel):
    utility: str = Field(..., min_length=1)
    crrem_pathway_version: str = Field(..., min_length=1)
    intensity_unit: str = Field(..., min_length=1)
    portfolio_intensity: Optional[float] = Field(None, ge=0)
    decarbonization_pathway: Optional[List[DecarbonizationPathwayPoint]] = Field(None, min_items=5)
    eligibility: Eligibility
    stranded: Stranded


class CRREMPathwayAnalysis(BaseModel):
    energy: CRREMSection
    ghg: CRREMSection


# Building Certifications in Performance Insights
class BuildingCertificationsCountryData(BaseModel):
    country: str = Field(..., min_length=2)
    gav_percentage: float = Field(..., ge=0, le=100)
    portfolio_characteristics: PortfolioCharacteristics
    tables: List[CertificationTable] = Field(..., min_items=3, max_items=3)


class BuildingCertificationsSection(BaseModel):
    property_type: str = Field(..., min_length=2)
    gav_percentage: float = Field(..., ge=0, le=100)
    countries: List[BuildingCertificationsCountryData] = Field(..., min_items=1)


class BuildingCertifications(BaseModel):
    area_unit: AreaUnit
    sections: List[BuildingCertificationsSection] = Field(..., min_items=1)


# Performance Insights
class PerformanceInsights(BaseModel):
    energy: ResourceSection
    ghg: ResourceSection
    water: ResourceSection
    waste: ResourceSection
    building_certifications: BuildingCertifications


# Asset Allocation Models
class AllocationItem(BaseModel):
    percent: str = Field(..., regex=r"^(100|[1-9][0-9]?|< 1|0)%$")

    class Config:
        extra = "allow"  # Allow region/asset_type/control fields


class Allocation(BaseModel):
    regional_allocation: List[AllocationItem] = Field(..., min_items=1)
    sector_allocation: List[AllocationItem] = Field(..., min_items=1)


class AssetsAllocation(BaseModel):
    entity_allocation: Allocation
    peers_allocation: Allocation
    customized_peers_allocation: Optional[Allocation] = None


# Portfolio Impact Models
class PortfolioImpactTarget(BaseModel):
    type: TargetType
    long_percent: Optional[str] = None
    baseline: Optional[int] = Field(None, ge=2000)
    end: Optional[int] = Field(None, ge=2020)


class PortfolioImpactItem(BaseModel):
    footprint: Optional[Any] = None
    lfl: Optional[Any] = None
    equivalent: Optional[Any] = None
    non_operational_consumption: Optional[str] = None
    target: PortfolioImpactTarget
    verification: str


class PortfolioImpact(BaseModel):
    summary: Optional[Any] = None

    class Config:
        extra = "allow"  # Allow dynamic keys for different impact types


# Entity Model
class Entity(BaseModel):
    geography: str
    sector: str
    legal_status: LegalStatus
    reporting_period: ReportingPeriod
    gav: Money


# Peer Group Models
class PeerGroup(BaseModel):
    count: Optional[int] = None
    geography: Optional[List[Union[str, List[str]]]] = None
    sector: Optional[List[Union[str, List[str]]]] = None
    legal_status: Optional[List[str]] = None
    avg_gav: Money


# Summary Model
class Summary(BaseModel):
    rating: Rating
    scores: Scores
    characteristics: Characteristics
    peer_group_rank: Optional[PeerGroupRank] = None
    customized_peer_group_rank: Optional[CustomizedPeerGroupRank] = None


# Scorecard Model
class Scorecard(BaseModel):
    summary: Summary
    rankings: Optional[Any] = None  # Reference to common schema
    gresb_model: Optional[Any] = None  # Reference to common schema
    esg_breakdown: Optional[Any] = None  # Reference to common schema
    trend: Optional[Any] = None  # Reference to common schema
    aspects: Optional[Any] = None  # Reference to common schema
    last_year_aspects: Optional[Any] = None  # Reference to common schema
    entity: Optional[Entity] = None
    peer_group: Optional[PeerGroup] = None
    customized_peer_group: Optional[PeerGroup] = None
    assets_allocation: Optional[AssetsAllocation] = None
    peer_group_constituents: Optional[Any] = None  # Reference to common schema
    customized_peer_group_constituents: Optional[Any] = None  # Reference to common schema
    portfolio_impact: Optional[PortfolioImpact] = None
    portfolio_intensities: Optional[Dict[str, IntensitySection]] = None
    targets: Optional[Targets] = None
    building_certifications: Optional[List[CertificationTable]] = None


# Indicator Breakdown Models
class IndicatorBreakdownManagement(BaseModel):
    leadership: Optional[Any] = None  # Reference to aspect schema
    policies: Optional[Any] = None
    reporting: Optional[Any] = None
    risk_management: Optional[Any] = None
    stakeholder_engagement: Optional[Any] = None


class IndicatorBreakdownPerformance(BaseModel):
    risk_assessment: Optional[Any] = None
    tenants_community: Optional[Any] = None
    data_monitoring_review: Optional[Any] = None


class IndicatorBreakdownDevelopment(BaseModel):
    dev_esg_requirements: Optional[Any] = None
    dev_materials: Optional[Any] = None
    dev_building_certifications: Optional[Any] = None
    dev_energy: Optional[Any] = None
    dev_water: Optional[Any] = None
    dev_waste: Optional[Any] = None
    dev_stakeholder_engagement: Optional[Any] = None
    dev_targets: Optional[Any] = None


class IndicatorBreakdown(BaseModel):
    management: Optional[IndicatorBreakdownManagement] = None
    performance: Optional[IndicatorBreakdownPerformance] = None
    development: Optional[IndicatorBreakdownDevelopment] = None


# Score Summary Model
class ScoresSummary(BaseModel):
    management: Optional[Any] = None  # Reference to common schema
    performance: Optional[Any] = None
    development: Optional[Any] = None


class LenderReport(BaseModel):
    meta: Optional[Any] = None  # Reference to common schema
    scorecards: Dict[ScorecardType, Scorecard]


# Main Real Estate Report Model
class RealEstateReport(BaseModel):
    meta: Optional[Any] = None  # Reference to common schema
    scorecards: Dict[ScorecardType, Scorecard]
    score_summary: Optional[ScoresSummary] = None
    performance_insights: Optional[PerformanceInsights] = None
    crrem_pathway_analysis: Optional[CRREMPathwayAnalysis] = None
    validation: Optional[Any] = None  # Reference to common schema
    indicator_breakdown: Optional[IndicatorBreakdown] = None

    class Config:
        extra = "allow"  # Allow additional properties as specified in schema
        min_anystr_length = 1
        validate_assignment = True


# Additional helper models for validation
class PercentPattern(BaseModel):
    """Helper for percent validation"""
    @field_validator('*', pre=True)
    def validate_percent_string(cls, v):
        if isinstance(v, str) and v.endswith('%'):
            try:
                return float(v.replace('%', ''))
            except ValueError:
                raise ValueError(f"Invalid percent format: {v}")
        return v 