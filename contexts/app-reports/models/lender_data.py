import polars as pl
from pydantic import BaseModel

class SummaryDataResponses(BaseModel):

    responses: pl.DataFrame 

    rating: str # come from responses
    stars: int
    gresb_score: float
    management_score: float
    strategy: str
    legal_status: str
    property_type: str
    location: str

    @classmethod
    def from_responses(cls, responses: pl.DataFrame, response_id: str):
        return cls(
            rating=responses.filter(pl.col("response_id") == response_id).select("GRESB_RATING").item(),
            stars=responses.filter(pl.col("response_id") == response_id).select("GRESB_RATING").item(),
            gresb_score=responses.filter(pl.col("response_id") == response_id).select("GRESB_SCORE").item(),
            management_score=responses.filter(pl.col("response_id") == response_id).select("MANAGEMENT_SCORE").item(),
            strategy=responses.filter(pl.col("response_id") == response_id).select("STRATEGY").item(),
            legal_status=responses.filter(pl.col("response_id") == response_id).select("LEGAL_STATUS").item(),
            property_type=responses.filter(pl.col("response_id") == response_id).select("PROPERTY_TYPE").item(),
            location=responses.filter(pl.col("response_id") == response_id).select("LOCATION").item(),
        )


class SummaryDataPeerGroups(BaseModel):
    peer_groups: pl.DataFrame

    peer_group_rank: int # come from peer_groups
    peer_group_size: int

    @classmethod
    def from_peer_groups(cls, peer_groups: pl.DataFrame):
        return cls(peer_groups=peer_groups)


class SummaryData(SummaryDataResponses, SummaryDataPeerGroups):
    pass