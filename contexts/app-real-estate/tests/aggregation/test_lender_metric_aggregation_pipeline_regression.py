import pytest
import pandas as pd
import numpy as np

from app_real_estate.transformation.aggregation.lender_metric_aggregation_pipeline import (
    LenderMetricAggregationPipeline,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.common_aggregation_keys as agg_keys


class TestLenderMetricAggregationPipelineRegression:
    """Integration tests for LenderMetricAggregationPipeline - comprehensive regression testing."""

    @pytest.fixture
    def asset_data(self):
        """Create comprehensive mock asset data with all required columns for the pipeline."""
        return pd.DataFrame(
            {
                # Core asset identification
                ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6],
                ac.response_id: [100, 100, 100, 200, 200, 200],
                ac.data_year: [2023] * 6,
                ac.survey_year: [2023] * 6,
                ac.snapshot_id: [1] * 6,
                ac.country: ["US", "US", "UK", "UK", "DE", "DE"],
                ac.property_type_code: ["OCHI", "OCHI", "ORTS", "ORTS", "OCHI", "ORTS"],
                ac.property_sector: [
                    "Office",
                    "Office",
                    "Retail",
                    "Retail",
                    "Office",
                    "Retail",
                ],
                ac.company_fund_id: [1001, 1001, 1001, 2001, 2001, 2001],
                # Asset size and ownership
                ac.asset_size_m2: [1000.0, 1500.0, 800.0, 1200.0, 2000.0, 900.0],
                ac.asset_size_sqft: [
                    10764.0,
                    16146.0,
                    8611.0,
                    12917.0,
                    21528.0,
                    9688.0,
                ],
                ac.asset_size: [1000.0, 1500.0, 800.0, 1200.0, 2000.0, 900.0],
                ac.area_unit: ["m2"] * 6,
                ac.asset_ownership: [100.0, 80.0, 90.0, 100.0, 75.0, 85.0],
                ac.asset_ownership_fraction: [1.0, 0.8, 0.9, 1.0, 0.75, 0.85],
                # Operational status (required for filtering)
                ac.standing_investment_for_aggregation: [True] * 6,
                ac.owned_for_aggregation: [True] * 6,
                # Comprehensive energy consumption data
                ec.en_abs: [5000.0, 7500.0, 4000.0, 6000.0, 10000.0, 4500.0],
                ec.en_abs_ly: [5200.0, 7800.0, 4200.0, 6300.0, 10500.0, 4700.0],
                ec.en_abs_in: [4500.0, 6750.0, 3600.0, 5400.0, 9000.0, 4050.0],
                ec.en_abs_f_kwh: [
                    2000000.0,
                    3000000.0,
                    1600000.0,
                    2400000.0,
                    4000000.0,
                    1800000.0,
                ],
                ec.en_abs_e_kwh: [
                    2000000.0,
                    3000000.0,
                    1600000.0,
                    2400000.0,
                    4000000.0,
                    1800000.0,
                ],
                ec.en_abs_d_kwh: [
                    1000000.0,
                    1500000.0,
                    800000.0,
                    1200000.0,
                    2000000.0,
                    900000.0,
                ],
                # Fuel consumption columns (required by add_energy_consumption_per_energy_type)
                ec.en_abs_wf: [500.0, 750.0, 400.0, 600.0, 1000.0, 450.0],
                ec.en_abs_lc_bsf: [100.0, 150.0, 80.0, 120.0, 200.0, 90.0],
                ec.en_abs_lc_bcf: [100.0, 150.0, 80.0, 120.0, 200.0, 90.0],
                ec.en_abs_lc_tf: [150.0, 225.0, 120.0, 180.0, 300.0, 135.0],
                ec.en_abs_tc_tf: [100.0, 150.0, 80.0, 120.0, 200.0, 90.0],
                ec.en_abs_lc_of: [50.0, 75.0, 40.0, 60.0, 100.0, 45.0],
                ec.en_abs_tc_of: [50.0, 75.0, 40.0, 60.0, 100.0, 45.0],
                # District heating/cooling columns (required by add_energy_consumption_per_energy_type)
                ec.en_abs_wd: [200.0, 300.0, 160.0, 240.0, 400.0, 180.0],
                ec.en_abs_lc_bsd: [50.0, 75.0, 40.0, 60.0, 100.0, 45.0],
                ec.en_abs_lc_bcd: [50.0, 75.0, 40.0, 60.0, 100.0, 45.0],
                ec.en_abs_lc_td: [50.0, 75.0, 40.0, 60.0, 100.0, 45.0],
                ec.en_abs_tc_td: [50.0, 75.0, 40.0, 60.0, 100.0, 45.0],
                # Electricity consumption columns (required by add_energy_consumption_per_energy_type)
                ec.en_abs_we: [2000.0, 3000.0, 1600.0, 2400.0, 4000.0, 1800.0],
                ec.en_abs_lc_bse: [400.0, 600.0, 320.0, 480.0, 800.0, 360.0],
                ec.en_abs_lc_bce: [400.0, 600.0, 320.0, 480.0, 800.0, 360.0],
                ec.en_abs_lc_te: [600.0, 900.0, 480.0, 720.0, 1200.0, 540.0],
                ec.en_abs_tc_te: [400.0, 600.0, 320.0, 480.0, 800.0, 360.0],
                ec.en_abs_lc_oe: [100.0, 150.0, 80.0, 120.0, 200.0, 90.0],
                ec.en_abs_tc_oe: [100.0, 150.0, 80.0, 120.0, 200.0, 90.0],
                # Renewable energy data
                ec.en_ren_abs: [1000.0, 1500.0, 800.0, 1200.0, 2000.0, 900.0],
                ec.en_ren_abs_ly: [900.0, 1400.0, 750.0, 1100.0, 1900.0, 850.0],
                ec.en_ren_ons_con: [500.0, 750.0, 400.0, 600.0, 1000.0, 450.0],
                ec.en_ren_ons_exp: [200.0, 300.0, 160.0, 240.0, 400.0, 180.0],
                ec.en_ren_ons_tpt: [300.0, 450.0, 240.0, 360.0, 600.0, 270.0],
                ec.en_ren_ofs_pbl: [200.0, 300.0, 160.0, 240.0, 400.0, 180.0],
                ec.en_ren_ofs_pbt: [100.0, 150.0, 80.0, 120.0, 200.0, 90.0],
                # GHG emissions data
                gc.ghg_abs: [100.0, 150.0, 80.0, 120.0, 200.0, 90.0],
                gc.ghg_abs_ly: [105.0, 155.0, 85.0, 125.0, 210.0, 95.0],
                gc.ghg_abs_s1: [50.0, 75.0, 40.0, 60.0, 100.0, 45.0],
                gc.ghg_abs_s2_lb: [30.0, 45.0, 24.0, 36.0, 60.0, 27.0],
                gc.ghg_abs_s2_mb: [10.0, 15.0, 8.0, 12.0, 20.0, 9.0],
                gc.ghg_abs_s3: [10.0, 15.0, 8.0, 12.0, 20.0, 9.0],
                gc.ghg_abs_offset: [5.0, 7.5, 4.0, 6.0, 10.0, 4.5],
                gc.ghg_abs_in: [90.0, 135.0, 72.0, 108.0, 180.0, 81.0],
                # Asset control data - essential for coverage calculations
                ac.whole_building: [True, True, False, False, True, False],
                ac.tenant_ctrl: [False, False, True, True, False, True],
                ac.asset_size_tenant_m2: [np.nan, np.nan, 600.0, 900.0, np.nan, 600.0],
                ac.asset_size_tenant_landlord_m2: [
                    np.nan,
                    np.nan,
                    200.0,
                    300.0,
                    np.nan,
                    300.0,
                ],
                ac.asset_size_shared_m2: [np.nan, np.nan, 800.0, 1200.0, np.nan, 900.0],
                ac.asset_size_common_m2: [np.nan, np.nan, 100.0, 150.0, np.nan, 100.0],
                ac.asset_size_tenant_tenant_m2: [
                    np.nan,
                    np.nan,
                    500.0,
                    750.0,
                    np.nan,
                    500.0,
                ],
                # Vacancy data for intensity calculations
                ac.asset_vacancy: [5.0, 8.0, 10.0, 12.0, 3.0, 15.0],
                # Required area weight columns for owned area weight calculations
                ec.en_area_weight_lc: [950.0, 1350.0, 640.0, 1020.0, 1960.0, 675.0],
                ec.en_area_weight_tc: [1000.0, 1425.0, 680.0, 1080.0, 2000.0, 720.0],
                gc.ghg_area_weight_s12: [900.0, 1275.0, 600.0, 960.0, 1900.0, 630.0],
                gc.ghg_area_weight_s3: [850.0, 1200.0, 560.0, 900.0, 1800.0, 585.0],
                wc.wat_area_weight_lc: [880.0, 1245.0, 584.0, 936.0, 1860.0, 612.0],
                wc.wat_area_weight_tc: [920.0, 1305.0, 616.0, 984.0, 1940.0, 648.0],
                # Area percentages for coverage calculations (often needed)
                ec.en_area_p_lc: [95.0, 90.0, 80.0, 85.0, 98.0, 75.0],
                ec.en_area_p_tc: [100.0, 95.0, 85.0, 90.0, 100.0, 80.0],
                gc.ghg_area_p_s12: [90.0, 85.0, 75.0, 80.0, 95.0, 70.0],
                gc.ghg_area_p_s3: [85.0, 80.0, 70.0, 75.0, 90.0, 65.0],
                # Data availability columns (required for intensity filtering)
                ec.en_days_data_avail: [365, 365, 300, 320, 365, 280],
                ec.en_max_days_data_avail: [365, 365, 365, 365, 365, 365],
                gc.ghg_days_data_avail: [365, 365, 290, 310, 365, 270],
                gc.ghg_max_days_data_avail: [365, 365, 365, 365, 365, 365],
                # Coverage percentage columns (required for intensity calculations)
                ec.en_area_time_cov_p: [100.0, 90.0, 75.0, 88.0, 100.0, 77.0],
                ec.en_area_time_cov_p_lc: [95.0, 85.0, 70.0, 83.0, 98.0, 72.0],
                ec.en_area_time_cov_p_tc: [100.0, 95.0, 80.0, 93.0, 100.0, 82.0],
                gc.ghg_area_time_cov_p: [95.0, 88.0, 73.0, 85.0, 97.0, 75.0],
                gc.ghg_area_time_cov_p_s12: [90.0, 83.0, 68.0, 80.0, 95.0, 70.0],
                gc.ghg_area_time_cov_p_s3: [85.0, 78.0, 63.0, 75.0, 90.0, 65.0],
                # Outlier status flags (often checked in pipelines)
                ec.en_int_outlier_status: ["none"] * 6,
                ec.en_lfl_outlier_status: ["none"] * 6,
                gc.ghg_int_outlier_status: ["none"] * 6,
                gc.ghg_lfl_outlier_status: ["none"] * 6,
                # Area time weight columns (required for coverage calculations)
                ec.en_area_time_weight_lc: [
                    365000.0,
                    547500.0,
                    292000.0,
                    438000.0,
                    730000.0,
                    328500.0,
                ],
                ec.en_area_time_weight_tc: [
                    365000.0,
                    547500.0,
                    292000.0,
                    438000.0,
                    730000.0,
                    328500.0,
                ],
                gc.ghg_area_time_weight_s12: [
                    328500.0,
                    465750.0,
                    219000.0,
                    350400.0,
                    693500.0,
                    229950.0,
                ],
                gc.ghg_area_time_weight_s3: [
                    310250.0,
                    438000.0,
                    204400.0,
                    328500.0,
                    657000.0,
                    213525.0,
                ],
                # LFL (Like-for-Like) consumption columns (required for MWh calculations)
                ec.en_lfl_abs: [4500.0, 6750.0, 3600.0, 5400.0, 9000.0, 4050.0],
                ec.en_lfl_abs_ly: [4700.0, 7000.0, 3800.0, 5600.0, 9500.0, 4200.0],
                ec.en_lfl_abs_lc: [2250.0, 3375.0, 1800.0, 2700.0, 4500.0, 2025.0],
                ec.en_lfl_abs_tc: [2250.0, 3375.0, 1800.0, 2700.0, 4500.0, 2025.0],
                ec.en_lfl_abs_in: [4050.0, 6075.0, 3240.0, 4860.0, 8100.0, 3645.0],
                gc.ghg_lfl_abs: [90.0, 135.0, 72.0, 108.0, 180.0, 81.0],
                gc.ghg_lfl_abs_ly: [95.0, 140.0, 76.0, 112.0, 190.0, 85.0],
                # Additional energy consumption columns
                ec.en_abs_nopr_ev: [100.0, 150.0, 80.0, 120.0, 200.0, 90.0],
                ec.en_ren_abs_consumed_kwh: [
                    800000.0,
                    1200000.0,
                    640000.0,
                    960000.0,
                    1600000.0,
                    720000.0,
                ],
                # GHG detailed scope breakdown columns (required for scope calculations)
                gc.ghg_abs_s1_w: [25.0, 37.5, 20.0, 30.0, 50.0, 22.5],
                gc.ghg_abs_s1_o: [25.0, 37.5, 20.0, 30.0, 50.0, 22.5],
                gc.ghg_abs_s2_lb_w: [15.0, 22.5, 12.0, 18.0, 30.0, 13.5],
                gc.ghg_abs_s2_lb_o: [15.0, 22.5, 12.0, 18.0, 30.0, 13.5],
                gc.ghg_abs_s2_mb_w: [5.0, 7.5, 4.0, 6.0, 10.0, 4.5],
                gc.ghg_abs_s2_mb_o: [5.0, 7.5, 4.0, 6.0, 10.0, 4.5],
                gc.ghg_abs_s3_w: [5.0, 7.5, 4.0, 6.0, 10.0, 4.5],
                gc.ghg_abs_s3_o: [5.0, 7.5, 4.0, 6.0, 10.0, 4.5],
                # Area coverage percentage columns (required for aggregation)
                ec.en_area_cov_p: [97.5, 92.5, 82.5, 87.5, 99.0, 77.5],
                gc.ghg_area_cov_p: [92.5, 86.5, 74.0, 82.5, 96.0, 72.5],
            }
        )

    @pytest.fixture
    def expected_metrics_sector_ctr(self):
        """Expected output for metrics_sector_ctr from the pipeline (static regression data)."""
        return pd.DataFrame(
            {
                ac.property_sector: ["Office", "Office", "Retail", "Retail", "Retail"],
                ac.country: ["DE", "US", "DE", "UK", "UK"],
                ac.response_id: [200, 100, 200, 100, 200],
                ac.data_year: [2023, 2023, 2023, 2023, 2023],
                ec.asset_vacancy_energy_intensity: [
                    np.nan,
                    np.nan,
                    np.nan,
                    np.nan,
                    np.nan,
                ],
                gc.asset_vacancy_ghg_intensity: [
                    np.nan,
                    np.nan,
                    np.nan,
                    np.nan,
                    np.nan,
                ],
                ec.asset_size_energy_intensity_m2: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.asset_size_ghg_intensity_m2: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.asset_size_energy_intensity_sqft: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.asset_size_ghg_intensity_sqft: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.energy_efficiency_area_m2: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.energy_efficiency_area_sqft: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_area_time_cov_p_lc_agg: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_area_time_cov_p_tc_agg: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_area_time_cov_p_s12_agg: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_area_time_cov_p_s3_agg: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_ren_ons_con: [1000.0, 1250.0, 450.0, 400.0, 600.0],
                ec.en_ren_ons_exp: [400.0, 500.0, 180.0, 160.0, 240.0],
                ec.en_ren_ons_tpt: [600.0, 750.0, 270.0, 240.0, 360.0],
                ec.en_ren_ofs_pbl: [400.0, 500.0, 180.0, 160.0, 240.0],
                ec.en_ren_ofs_pbt: [200.0, 250.0, 90.0, 80.0, 120.0],
                ec.en_efficiency_int_kwh_m2_accepted: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_scored_int_ton_m2_accepted: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_scored_int_kg_m2_accepted: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_efficiency_int_kwh_sqft_accepted: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_scored_int_ton_sqft_accepted: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_scored_int_kg_sqft_accepted: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_int_eligible: [0, 0, 0, 0, 0],
                gc.ghg_int_eligible: [0, 0, 0, 0, 0],
                "asset_count": [1, 2, 1, 1, 1],
                ec.en_area_p_lc: [98.0, 92.27272727272727, 75.0, 80.0, 85.0],
                ec.en_area_p_tc: [100.0, 97.27272727272727, 80.0, 85.0, 90.0],
                gc.ghg_area_p_s12: [95.0, 87.27272727272727, 70.0, 75.0, 80.0],
                gc.ghg_area_p_s3: [90.0, 82.27272727272727, 65.0, 70.0, 75.0],
                ac.asset_size_owned_sqft: [0.0, 0.0, 0.0, 0.0, 0.0],
                ac.asset_size_owned_m2: [1500.0, 2200.0, 765.0, 720.0, 1200.0],
                ec.en_abs_mwh: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_abs: [10000.0, 12500.0, 4500.0, 4000.0, 6000.0],
                ec.en_abs_f_kwh: [
                    4000000.0,
                    5000000.0,
                    1800000.0,
                    1600000.0,
                    2400000.0,
                ],
                ec.en_abs_e_kwh: [
                    4000000.0,
                    5000000.0,
                    1800000.0,
                    1600000.0,
                    2400000.0,
                ],
                ec.en_abs_d_kwh: [2000000.0, 2500000.0, 900000.0, 800000.0, 1200000.0],
                ec.en_abs_f_mwh: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_abs_e_mwh: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_abs_d_mwh: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_abs: [200.0, 250.0, 90.0, 80.0, 120.0],
                gc.ghg_abs_net: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_ren_abs_mwh: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_ren_abs: [2000.0, 2500.0, 900.0, 800.0, 1200.0],
                ec.en_ren_abs_consumed_mwh: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_abs_offset: [10.0, 12.5, 4.5, 4.0, 6.0],
                ec.en_area_time_cov_p: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_area_time_cov_p: [0.0, 0.0, 0.0, 0.0, 0.0],
                ec.en_area_cov_p: [99.0, 190.0, 77.5, 82.5, 87.5],
                gc.ghg_area_cov_p: [96.0, 179.0, 72.5, 74.0, 82.5],
                ec.en_abs_nopr_ev_mwh: [0.0, 0.0, 0.0, 0.0, 0.0],
                gc.ghg_abs_s1: [100.0, 125.0, 45.0, 40.0, 60.0],
                gc.ghg_abs_s2_lb: [60.0, 75.0, 27.0, 24.0, 36.0],
                gc.ghg_abs_s2_mb: [20.0, 25.0, 9.0, 8.0, 12.0],
                gc.ghg_abs_s3: [20.0, 25.0, 9.0, 8.0, 12.0],
            }
        ).set_index([ac.property_sector, ac.country, ac.response_id, ac.data_year])

    @pytest.fixture
    def expected_metrics_portfolio(self):
        """Expected output for metrics_portfolio from the pipeline (static regression data)."""
        return pd.DataFrame(
            {
                ac.response_id: [100, 200],
                ac.data_year: [2023, 2023],
                ec.asset_vacancy_energy_intensity: [np.nan, np.nan],
                gc.asset_vacancy_ghg_intensity: [np.nan, np.nan],
                ec.asset_size_energy_intensity_m2: [0.0, 0.0],
                gc.asset_size_ghg_intensity_m2: [0.0, 0.0],
                ec.asset_size_energy_intensity_sqft: [0.0, 0.0],
                gc.asset_size_ghg_intensity_sqft: [0.0, 0.0],
                "energy_efficiency_area_m2": [0.0, 0.0],
                "energy_efficiency_area_sqft": [0.0, 0.0],
                "en_area_time_cov_p_lc_agg": [0.0, 0.0],
                "en_area_time_cov_p_tc_agg": [0.0, 0.0],
                "ghg_area_time_cov_p_s12_agg": [0.0, 0.0],
                "ghg_area_time_cov_p_s3_agg": [0.0, 0.0],
                "en_ren_ons_con": [1650.0, 2050.0],
                "en_ren_ons_exp": [660.0, 820.0],
                "en_ren_ons_tpt": [990.0, 1230.0],
                "en_ren_ofs_pbl": [660.0, 820.0],
                "en_ren_ofs_pbt": [330.0, 410.0],
                "en_efficiency_int_kwh_m2_accepted": [0.0, 0.0],
                "ghg_scored_int_ton_m2_accepted": [0.0, 0.0],
                "ghg_scored_int_kg_m2_accepted": [0.0, 0.0],
                "en_efficiency_int_kwh_sqft_accepted": [0.0, 0.0],
                "ghg_scored_int_ton_sqft_accepted": [0.0, 0.0],
                "ghg_scored_int_kg_sqft_accepted": [0.0, 0.0],
                "en_int_eligible": [0, 0],
                "ghg_int_eligible": [0, 0],
                "asset_count": [3, 3],
                "en_area_p_lc": [89.24657534246575, 88.41991341991341],
                "en_area_p_tc": [94.24657534246575, 92.12121212121212],
                "ghg_area_p_s12": [84.24657534246575, 84.28571428571429],
                "ghg_area_p_s3": [79.24657534246575, 79.28571428571429],
                "asset_size_owned_sqft": [0.0, 0.0],
                "asset_size_owned_m2": [2920.0, 3465.0],
                "en_abs_mwh": [0.0, 0.0],
                "en_abs": [16500.0, 20500.0],
                "en_abs_f_kwh": [6600000.0, 8200000.0],
                "en_abs_e_kwh": [6600000.0, 8200000.0],
                "en_abs_d_kwh": [3300000.0, 4100000.0],
                "en_abs_f_mwh": [0.0, 0.0],
                "en_abs_e_mwh": [0.0, 0.0],
                "en_abs_d_mwh": [0.0, 0.0],
                "ghg_abs": [330.0, 410.0],
                "ghg_abs_net": [0.0, 0.0],
                "en_ren_abs_mwh": [0.0, 0.0],
                "en_ren_abs": [3300.0, 4100.0],
                "en_ren_abs_consumed_mwh": [0.0, 0.0],
                "ghg_abs_offset": [16.5, 20.5],
                "en_area_time_cov_p": [0.0, 0.0],
                "ghg_area_time_cov_p": [0.0, 0.0],
                "en_area_cov_p": [272.5, 264.0],
                "ghg_area_cov_p": [253.0, 251.0],
                "en_abs_nopr_ev_mwh": [0.0, 0.0],
                "ghg_abs_s1": [165.0, 205.0],
                "ghg_abs_s2_lb": [99.0, 123.0],
                "ghg_abs_s2_mb": [33.0, 41.0],
                "ghg_abs_s3": [33.0, 41.0],
            }
        )  # NO .set_index() - keep response_id and data_year as regular columns

    def test_pipeline_comprehensive_functionality(self, asset_data):
        """
        Comprehensive integration test for LenderMetricAggregationPipeline.

        This test serves as a regression test by:
        1. Verifying the pipeline executes successfully with realistic data
        2. Checking that all expected output structures are present
        3. Validating that calculated values meet business constraints
        4. Ensuring deterministic results across runs
        """
        # Execute the pipeline
        metrics_sector_ctr, metrics_portfolio, processed_asset_data = (
            LenderMetricAggregationPipeline.process_asset_data(asset_data)
        )

        # Test 1: Verify return types and basic structure
        assert isinstance(
            metrics_sector_ctr, pd.DataFrame
        ), "metrics_sector_ctr should be a DataFrame"
        assert isinstance(
            metrics_portfolio, pd.DataFrame
        ), "metrics_portfolio should be a DataFrame"
        assert isinstance(
            processed_asset_data, pd.DataFrame
        ), "processed_asset_data should be a DataFrame"

        # Test 2: Verify data integrity - no loss of original assets
        assert len(processed_asset_data) == len(
            asset_data
        ), "Pipeline should preserve all original assets"

        # Test 3: Verify basic aggregation structure
        assert not metrics_sector_ctr.empty, "metrics_sector_ctr should not be empty"
        assert not metrics_portfolio.empty, "metrics_portfolio should not be empty"

        # Test 4: Verify aggregation includes expected response IDs
        expected_response_ids = asset_data[ac.response_id].unique()
        portfolio_response_ids = metrics_portfolio[ac.response_id].unique()
        assert set(expected_response_ids) == set(
            portfolio_response_ids
        ), "Portfolio aggregation should include all response IDs from input data"

        # Test 5: Verify that new columns were created (key regression check)
        original_columns = set(asset_data.columns)
        new_columns = set(processed_asset_data.columns) - original_columns

        # Check that some new columns were created
        assert len(new_columns) > 0, "Pipeline should create new calculated columns"

        # Check for specific key columns that should be created
        key_columns_expected = [
            ec.en_ren_ons,  # from add_renewable_generation_per_area
            ec.en_ren_ofs,  # from add_renewable_generation_per_area
            ec.en_ren_abs_consumed_kwh,  # from add_consumed_renewable_energy_consumption
            ec.en_ren_rate,  # from add_renewable_energy_rate
            sc.asset_size_owned_m2,  # from AddAreaOwnershipWeightFactor
        ]

        key_columns_present = [
            col for col in key_columns_expected if col in processed_asset_data.columns
        ]
        assert (
            len(key_columns_present) >= 3
        ), f"Expected at least 3 key columns to be created, found {len(key_columns_present)}: {key_columns_present}"

        # Test 6: Verify numerical constraints (regression checks for business logic)
        if ec.en_ren_rate in processed_asset_data.columns:
            ren_rates = processed_asset_data[ec.en_ren_rate].dropna()
            if len(ren_rates) > 0:
                assert (
                    ren_rates >= 0
                ).all(), "Renewable energy rates should be non-negative"
                assert (
                    ren_rates <= 100
                ).all(), "Renewable energy rates should not exceed 100%"

        if sc.asset_size_owned_m2 in processed_asset_data.columns:
            owned_sizes = processed_asset_data[sc.asset_size_owned_m2].dropna()
            if len(owned_sizes) > 0:
                assert (owned_sizes > 0).all(), "Asset size owned should be positive"

        # Test 7: Verify aggregation structure (key for regression testing)
        if not metrics_sector_ctr.empty:
            # Should have multi-index with expected levels
            index_names = metrics_sector_ctr.index.names
            expected_index_keys = agg_keys.propertysector_country_responseid + [
                ac.data_year
            ]
            assert any(
                key in index_names for key in expected_index_keys
            ), f"Expected some aggregation keys {expected_index_keys} in index {index_names}"

    @pytest.mark.parametrize(
        "missing_column",
        [
            ac.portfolio_asset_id,
            ac.response_id,
            ac.data_year,
            ac.asset_size_m2,
            ac.asset_ownership,
        ],
    )
    def test_pipeline_required_columns(self, asset_data, missing_column):
        """Test that pipeline fails gracefully when required columns are missing."""
        # Remove a required column
        incomplete_data = asset_data.drop(columns=[missing_column])

        # Pipeline should provide clear error
        with pytest.raises((KeyError, ValueError, AttributeError)) as exc_info:
            LenderMetricAggregationPipeline.process_asset_data(incomplete_data)

        # Error should be informative
        error_message = str(exc_info.value).lower()
        assert (
            missing_column.lower() in error_message
            or "key" in error_message
            or "column" in error_message
            or "missing" in error_message
        ), f"Error should clearly indicate missing column {missing_column}"

    def test_pipeline_static_regression_metrics_sector_ctr(
        self, asset_data, expected_metrics_sector_ctr
    ):
        """
        Regression test against static expected output for metrics_sector_ctr.

        This test ensures that the pipeline produces the exact same aggregated results
        as captured in the baseline data, protecting against unintended changes
        in calculation logic.
        """
        # Execute the pipeline
        metrics_sector_ctr, metrics_portfolio, processed_asset_data = (
            LenderMetricAggregationPipeline.process_asset_data(asset_data)
        )

        # Sort both DataFrames by index to ensure consistent order
        actual_sector_ctr = metrics_sector_ctr.sort_index()
        expected_sector_ctr = expected_metrics_sector_ctr.sort_index()

        # Verify the structure first
        assert (
            actual_sector_ctr.shape == expected_sector_ctr.shape
        ), f"Shape mismatch: actual {actual_sector_ctr.shape} vs expected {expected_sector_ctr.shape}"

        assert list(actual_sector_ctr.index.names) == list(
            expected_sector_ctr.index.names
        ), f"Index names mismatch: actual {actual_sector_ctr.index.names} vs expected {expected_sector_ctr.index.names}"

        # Test key columns individually for better error messages
        key_columns_to_test = [
            "asset_count",
            "en_abs",
            "ghg_abs",
            "en_ren_abs",
            "asset_size_owned_m2",
            "en_ren_ons_con",
            "en_ren_ons_exp",
            "ghg_abs_s1",
            "ghg_abs_s2_lb",
            "en_area_p_lc",
            "en_area_p_tc",
            "ghg_area_p_s12",
            "ghg_area_p_s3",
        ]

        for col in key_columns_to_test:
            if col in actual_sector_ctr.columns and col in expected_sector_ctr.columns:
                actual_values = actual_sector_ctr[col].values
                expected_values = expected_sector_ctr[col].values

                np.testing.assert_allclose(
                    actual_values,
                    expected_values,
                    rtol=1e-10,
                    atol=1e-12,
                    err_msg=f"Column {col} values differ from expected",
                )

        # Full DataFrame comparison for complete regression protection
        pd.testing.assert_frame_equal(
            actual_sector_ctr,
            expected_sector_ctr,
            check_dtype=False,
            rtol=1e-10,
            atol=1e-12,
        )

    def test_pipeline_static_regression_metrics_portfolio(
        self, asset_data, expected_metrics_portfolio
    ):
        """
        Regression test against static expected output for metrics_portfolio.

        This test ensures that the pipeline produces the exact same portfolio-level
        aggregated results as captured in the baseline data, protecting against
        unintended changes in calculation logic.
        """
        # Execute the pipeline
        metrics_sector_ctr, metrics_portfolio, processed_asset_data = (
            LenderMetricAggregationPipeline.process_asset_data(asset_data)
        )

        # Sort both DataFrames by response_id to ensure consistent order
        actual_portfolio = metrics_portfolio.sort_values("response_id").reset_index(
            drop=True
        )
        expected_portfolio = expected_metrics_portfolio.sort_values(
            "response_id"
        ).reset_index(drop=True)

        # Verify the structure first
        assert (
            actual_portfolio.shape == expected_portfolio.shape
        ), f"Shape mismatch: actual {actual_portfolio.shape} vs expected {expected_portfolio.shape}"

        # Test key columns individually for better error messages
        key_portfolio_columns_to_test = [
            "response_id",
            "data_year",
            "asset_count",
            "en_abs",
            "ghg_abs",
            "en_ren_abs",
            "asset_size_owned_m2",
            "en_ren_ons_con",
            "en_ren_ons_exp",
            "ghg_abs_s1",
            "ghg_abs_s2_lb",
            "en_area_p_lc",
            "en_area_p_tc",
        ]

        for col in key_portfolio_columns_to_test:
            if col in actual_portfolio.columns and col in expected_portfolio.columns:
                actual_values = actual_portfolio[col].values
                expected_values = expected_portfolio[col].values

                # Use appropriate comparison based on data type
                if pd.api.types.is_numeric_dtype(actual_values):
                    np.testing.assert_allclose(
                        actual_values,
                        expected_values,
                        rtol=1e-10,
                        atol=1e-12,
                        err_msg=f"Portfolio column {col} values differ from expected",
                    )
                else:
                    np.testing.assert_array_equal(
                        actual_values,
                        expected_values,
                        err_msg=f"Portfolio column {col} values differ from expected",
                    )

        # Full DataFrame comparison for complete regression protection
        pd.testing.assert_frame_equal(
            actual_portfolio,
            expected_portfolio,
            check_dtype=False,
            rtol=1e-10,
            atol=1e-12,
        )

        # Verify key business relationships between sector and portfolio levels
        # Portfolio totals should equal sum of sector totals for same response_id
        sector_totals_by_response = metrics_sector_ctr.groupby("response_id").agg(
            {
                "asset_count": "sum",
                "en_abs": "sum",
                "ghg_abs": "sum",
                "asset_size_owned_m2": "sum",
            }
        )

        # Even better approach - temporarily set index for easier lookup:
        actual_portfolio_indexed = actual_portfolio.set_index("response_id")

        for response_id in [100, 200]:
            portfolio_row = actual_portfolio_indexed.loc[response_id]
            sector_totals = sector_totals_by_response.loc[response_id]

            assert (
                portfolio_row["asset_count"] == sector_totals["asset_count"]
            ), f"Asset count mismatch for response {response_id}"

            np.testing.assert_allclose(
                portfolio_row["en_abs"],
                sector_totals["en_abs"],
                rtol=1e-10,
                err_msg=f"Energy consumption mismatch for response {response_id}",
            )

            np.testing.assert_allclose(
                portfolio_row["ghg_abs"],
                sector_totals["ghg_abs"],
                rtol=1e-10,
                err_msg=f"GHG emissions mismatch for response {response_id}",
            )
