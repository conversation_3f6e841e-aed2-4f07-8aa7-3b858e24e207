## How to run the scoring model

The 'real estate' scoring model main script is `run_scoring_model.py`.
It takes a few arguments as input:
- `survey_year` which is mandatory and requires a valid assessment year.
- `--simulated` which is an optional argument set to true if specified. It indicates
to the script that the data input should be simulated. Automatically sets `--debug` to `True`.
- `--save-files` which is an optional argument set to true if specified. It indicates
to the script that the scores, benchmark groups and memberships should be saved on S3.
- `--debug` which is an optional argument set to true if specified. It indicates the script
to import the data files locally rather than on S3 (it saves time) and disables result saving to S3.

*To run the real estate the scoring model:*
```console
poetry run python -m contexts.app-real-estate.app_real_estate.run_scoring_model 2025
```

## Simulate new data

The default behaviour of the script is to look for 'real' asset-level data on S3.
If specified, you can ask it to fetch simulated data. Such files already exist on S3,
however you can decide to create new ones.

There are a few steps to simulate new data. First, in an R environment with access
to the `gresb-scoring` packages, update the local packages. Then, execute the script located in
`gresb-scoring/scripts/ab_test/generate_simulated_data_for_python.R` the following way:
```bash
Rscript scripts/ab_test/generate_simulated_data_for_python.R <survey_year> [--export_path]
```
The simulated data is saved in the `export-path` argument that you pass to the script.

In order to execute the scoring model using this simulated data, use the `--simulated` argument of the `run_scoring_model` script.
The data should be in the `tmp` and you should have the following architecture:
```
tmp
└───data
│   └───<survey_year>
│   │   │
│   │   └───input
│   │   │   └───asset_level_data.parquet
│   │   │   └───asset_certification_data.parquet
│   │   │   └───...
│   │   └───output
│   │   │
│   │   └───simulated # same input/output architecture in simulated
│   │   │   │
│   │   │   └───input
│   │   │   │   └───asset_level_data.parquet
│   │   │   │   └───asset_certification_data.parquet
│   │   │   │   └───...
│   │   │   └───output
```

Now that the data is simulated, if you want, you can either upload these files manually in S3
in the bucket `gresb-dev-gold-data/<survey_year>/real-estate/` with the role `dev`
or you can use the python functions available in the `extraction` folder of this
repository.

```python
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
import pandas as pd
from config import GOLD_DATA_BUCKET, RUNTIME

if RUNTIME != "dev":
    raise ValueError("Only run on dev, run `os.environ['RUNTIME'] = 'dev'`")

survey_year = 2024

# Import the local parquet files
asset_data = pd.read_parquet("~/gresb_scoring_user_files/data_exports/simulated-asset-level-data.parquet")
certifications_data = pd.read_parquet(
    "~/gresb_scoring_user_files/data_exports/simulated-building-certifications-data.parquet")
energy_ratings_data = pd.read_parquet("~/gresb_scoring_user_files/data_exports/simulated-energy-ratings-data.parquet")

filenames = [
    'simulated-asset-level-data',
    'simulated-building-certifications-data',
    'simulated-energy-ratings-data'
]
for filename in filenames:
    s3_gateway = DataS3Gateway()
    s3_gateway.export_data(
        S3File(
            bucket_name=GOLD_DATA_BUCKET,
            base_filename=f"{survey_year}/real-estate/{filename}",
            format="parquet",
        ),
    )
```

## Lender asset aggregation

How to run the lender asset aggregation pipeline:

```sh
poetry run python -m contexts.app-real-estate.app_real_estate.run_lender_asset_aggregation 2025
```

flags:
    --read-local: run the pipeline with local data, without this flag, it will use the data on S3
    --write-local: save the metrics to local storage, without this flag, it will save the metrics to S3
