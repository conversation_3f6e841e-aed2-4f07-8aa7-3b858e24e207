import argparse
import pandas as pd
import logging
import sys
from dotenv import load_dotenv

from app_real_estate.transformation.aggregation.lender_metric_aggregation_pipeline import (
    LenderMetricAggregationPipeline,
)
from app_real_estate.transformation.aggregation.metric_aggregation_pipeline import (
    MetricAggregationPipeline,
)
from config import GOLD_DATA_BUCKET
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from app_real_estate.models.input_models.r1_table_lender_model import R1TableLenderModel
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.transformation.metric_calculation.asset_filters import AssetFilters
from app_real_estate.transformation.aggregation.processor.lender_certifications_pre_processor import (
    LenderCertificationsPreProcessor,
)
from app_real_estate.transformation.aggregation.processor.lender_energy_ratings_pre_processor import (
    LenderEnergyRatingsPreProcessor,
)
from app_real_estate.utils.lender_metrics_writer import LenderAssetMetricsWriter


def run_lender_asset_aggregation(
    survey_year: int,
    logger: logging.Logger,
    read_local: bool = False,
    write_local: bool = False,
):
    """
    Run the lender asset aggregation pipeline.

    Args:
        survey_year: The survey year for the data
        logger: Logger instance for logging operations
        read_local: Whether to use local data and save locally (True) or use S3 (False)

    Raises:
        ValueError: If survey_year is invalid or data validation fails
        FileNotFoundError: If required input files are not found
        Exception: For other processing errors
    """
    # Input validation
    _validate_input_args(survey_year)

    logger.info(f"Starting lender asset aggregation for survey year {survey_year}")
    logger.info(f"Using {'local' if read_local else 'S3'} data source and storage")

    try:
        s3_gateway = DataS3Gateway()
    except Exception as e:
        logger.error(f"Failed to initialize S3 gateway: {str(e)}")
        raise

    # Non-aggregated asset level data with additional columns
    asset_level_data = None
    r1_table = None
    certification_data = None
    energy_ratings_data = None

    logger.info("Loading input data...")
    try:
        if read_local:
            asset_level_data = pd.read_parquet(
                f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/input/asset_level_data.parquet"
            )
            r1_table = pd.read_parquet(
                f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/input/r1_table_data.parquet"
            )
            certification_data = pd.read_parquet(
                f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/input/asset_certification_data.parquet"
            )
            energy_ratings_data = pd.read_parquet(
                f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/input/asset_energy_rating_data.parquet"
            )
            logger.info(
                f"Loaded local asset level data: {asset_level_data.shape[0]} assets, {asset_level_data.shape[1]} columns"
            )
        else:
            asset_level_data = s3_gateway.import_data(
                S3File(
                    bucket_name=GOLD_DATA_BUCKET,
                    base_filename=f"lender-asset-aggregation/rel/{survey_year}/input/asset_level_data",
                    format="parquet",
                )
            )
            r1_table = s3_gateway.import_data(
                S3File(
                    bucket_name=GOLD_DATA_BUCKET,
                    base_filename=f"master/rel/{survey_year}/input/r1_table_data",
                    format="parquet",
                )
            )
            certification_data = s3_gateway.import_data(
                S3File(
                    bucket_name=GOLD_DATA_BUCKET,
                    base_filename=f"master/rel/{survey_year}/input/asset_certification_data",
                    format="parquet",
                )
            )
            energy_ratings_data = s3_gateway.import_data(
                S3File(
                    bucket_name=GOLD_DATA_BUCKET,
                    base_filename=f"master/rel/{survey_year}/input/asset_energy_rating_data",
                    format="parquet",
                )
            )
            logger.info(
                f"Loaded asset level data from S3: {asset_level_data.shape[0]} assets, {asset_level_data.shape[1]} columns"
            )

    except FileNotFoundError as e:
        logger.error(f"Required input file not found: {str(e)}")
        raise
    except pd.errors.EmptyDataError as e:
        logger.error(f"Input file is empty or corrupted: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Failed to load input data: {str(e)}")
        raise

    # Validate and process data
    try:
        logger.info("Validating R1 table data")
        r1_table = R1TableLenderModel.validate(r1_table)

        # Validate that we have data
        if asset_level_data.empty:
            raise ValueError("Asset level data is empty")
        if r1_table.empty:
            raise ValueError("R1 table data is empty")
        if certification_data.empty:
            raise ValueError("Certification data is empty")
        if energy_ratings_data.empty:
            raise ValueError("Energy ratings data is empty")

        # Initialize the metrics writer
        metrics_writer = LenderAssetMetricsWriter(survey_year, logger)

        logger.info("Processing asset-level data through aggregation pipeline")
        lender_metric_aggregation_pipeline = LenderMetricAggregationPipeline()
        metrics_sector_ctr, metrics_portfolio, _ = (
            lender_metric_aggregation_pipeline.process_asset_data(asset_level_data)
        )
    except Exception as e:
        logger.error(f"Failed during data validation or initial processing: {str(e)}")
        raise

    # Save asset-level metrics using the utility class
    try:
        logger.info("Saving asset-level metrics")
        metrics_writer.write_lender_asset_metrics(
            metrics_sector_ctr, metrics_portfolio, write_local=write_local
        )
    except Exception as e:
        logger.error(f"Failed to save asset-level metrics: {str(e)}")
        raise

    logger.info("Preparing property type sector mapping")
    property_type_sector_mapping = asset_level_data[
        ["property_type_code", "property_sector"]
    ].drop_duplicates()
    property_type_sector_mapping.columns = ["PRT_TYPE", "PRT_SECTOR"]

    # forcing scoring coverage to be 1 for building certifications
    # because in lender assessment we don't score the building certifications
    logger.info("Preparing certification data for processing")
    certification_data[bc.scoring_coverage] = 1

    is_cy_opr = AssetFilters.filter_current_year_operational_assets(asset_level_data)
    asset_level_data_cy_opr = asset_level_data[is_cy_opr]
    logger.info(
        f"Filtered to {asset_level_data_cy_opr.shape[0]} current year operational assets"
    )

    certification_data_cy = certification_data[
        certification_data[ac.survey_year] == survey_year
    ]
    certification_data_cy = certification_data_cy.dropna(subset=[bc.year])
    certification_data_cy[bc.year] = certification_data_cy[bc.year].astype("int64")
    certification_data_cy[[ac.portfolio_asset_id, ac.survey_year, bc.year]].head()

    logger.info("Processing certification data through pre-processor")
    merged_certification_data = LenderCertificationsPreProcessor(
        certification_data=certification_data,
        asset_level_data=asset_level_data_cy_opr,
        r1_table=r1_table,
        survey_year=survey_year,
    ).process()

    logger.info("Processing energy ratings data through pre-processor")
    merged_energy_ratings_data = LenderEnergyRatingsPreProcessor(
        energy_ratings_data=energy_ratings_data,
        asset_level_data=asset_level_data_cy_opr,
        r1_table=r1_table,
        survey_year=survey_year,
    ).process()

    logger.info("Aggregating building certification metrics")
    br_cert_metrics = MetricAggregationPipeline.aggregate_certifications_data(
        r1_table=r1_table,
        certification_data=merged_certification_data,
        property_type_sector_mapping=property_type_sector_mapping,
    )

    # Save building certification metrics using the utility class
    logger.info("Saving building certification metrics")
    if write_local:
        metrics_writer.save_lender_bc_metrics_to_local(br_cert_metrics)
    else:
        metrics_writer.save_lender_bc_metrics_to_s3(br_cert_metrics)

    logger.info("Aggregating energy rating metrics")
    br_er_metrics = MetricAggregationPipeline.aggregate_ratings_data(
        energy_ratings_data=merged_energy_ratings_data,
        r1_table=r1_table,
        property_type_sector_mapping=property_type_sector_mapping,
    )

    # Save energy rating metrics using the utility class
    logger.info("Saving energy rating metrics")
    if write_local:
        metrics_writer.save_lender_er_metrics_to_local(br_er_metrics)
    else:
        metrics_writer.save_lender_er_metrics_to_s3(br_er_metrics)

    logger.info("Lender asset aggregation completed successfully")


def _validate_input_args(survey_year: int) -> None:
    """
    Validate input arguments for the aggregation pipeline.

    Args:
        survey_year: The survey year to validate

    Raises:
        ValueError: If arguments are invalid
    """
    if not isinstance(survey_year, int):
        raise ValueError(f"Survey year must be an integer, got {type(survey_year)}")


if __name__ == "__main__":
    load_dotenv()
    parser = argparse.ArgumentParser(description="Run lender asset aggregation")
    parser.add_argument(
        "survey_year", help="the reporting year of the data to score", type=int
    )
    parser.add_argument(
        "--read-local",
        help="whether to run the pipeline with locally stored data",
        action="store_true",
        default=False,  # Changed from True to False
    )
    parser.add_argument(
        "--write-local",
        help="whether to save the metrics to local storage",
        action="store_true",
        default=False,
    )

    args = parser.parse_args()

    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    logger.addHandler(handler)

    try:
        run_lender_asset_aggregation(
            survey_year=args.survey_year,
            logger=logger,
            read_local=args.read_local,
            write_local=args.write_local,
        )
    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        sys.exit(1)
    except FileNotFoundError as e:
        logger.error(f"File not found: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error during lender asset aggregation: {str(e)}")
        sys.exit(1)
