import logging
import os
from typing import Tuple
import pandas as pd

from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from config import GOLD_DATA_BUCKET


class LenderAssetMetricsWriter:
    """
    Utility class for saving lender asset metrics data to both S3 and local storage.
    Handles different types of metrics: asset-level, building certifications (BC), and energy ratings (ER).
    """

    # Define metric file mappings for different types
    ASSET_METRICS_FILES = {
        "metrics_sector_ctr": "metrics_sector_ctr.parquet",
        "metrics_portfolio": "metrics_portfolio.parquet",
    }

    BC_METRICS_FILES = {
        "ctr_prt_responseid_brand_scheme_certification": "ctr_prt_responseid_brand_scheme_certification_metrics.parquet",
        "ctr_prt_responseid_brand_certification": "ctr_prt_responseid_brand_certification_metrics.parquet",
        "ctr_prt_responseid_certification": "ctr_prt_responseid_certification_metrics.parquet",
        "portfolio_brand_scheme_certification": "portfolio_brand_scheme_certification_metrics.parquet",
        "portfolio_brand_certification": "portfolio_brand_certification_metrics.parquet",
        "portfolio_certification": "portfolio_certification_metrics.parquet",
    }

    ER_METRICS_FILES = {
        "ctr_prt_responseid_brand_er": "ctr_prt_responseid_brand_er_metrics.parquet",
        "ctr_prt_responseid_er": "ctr_prt_responseid_er_metrics.parquet",
        "portfolio_brand_er": "portfolio_brand_er_metrics.parquet",
        "portfolio_er": "portfolio_er_metrics.parquet",
    }

    def __init__(self, survey_year: int, logger: logging.Logger):
        """
        Initialize the LenderAssetMetricsWriter.

        Args:
            survey_year: The survey year for the data
            logger: Logger instance for logging operations
        """
        self.survey_year = survey_year
        self.logger = logger
        self.s3_gateway = DataS3Gateway()
        self.s3_path = f"lender-asset-aggregation/rel/{self.survey_year}/output"
        self.local_path = f"contexts/app-real-estate/app_real_estate/tmp/data/{self.survey_year}/rel/output"

    def _ensure_local_directory_exists(self) -> None:
        """Ensure the local output directory exists."""
        if not os.path.exists(self.local_path):
            os.makedirs(self.local_path, exist_ok=True)
            self.logger.info(f"Created local directory: {self.local_path}")

    def _check_s3_bucket_access(self) -> bool:
        """
        Check if the S3 bucket is accessible.

        Returns:
            bool: True if bucket is accessible, False otherwise
        """
        try:
            # Try to list objects in the bucket to verify access
            self.s3_gateway.fs.ls(f"{GOLD_DATA_BUCKET}/", detail=False)
            return True
        except Exception as e:
            self.logger.warning(f"S3 bucket access check failed: {str(e)}")
            return False

    def get_local_output_path(self) -> str:
        """
        Get the local output path.

        Returns:
            str: The local output directory path
        """
        return self.local_path

    def get_s3_output_path(self) -> str:
        """
        Get the S3 output path.

        Returns:
            str: The S3 output path
        """
        return f"s3://{GOLD_DATA_BUCKET}/{self.s3_path}"

    def validate_setup(self, write_local: bool = False) -> bool:
        """
        Validate that the writer is properly set up for the specified mode.

        Args:
            write_local: Whether to validate for local (True) or S3 (False) mode

        Returns:
            bool: True if setup is valid, False otherwise
        """
        try:
            if write_local:
                # For local mode, ensure directory can be created
                self._ensure_local_directory_exists()
                # Check if directory is writable
                test_file = os.path.join(self.local_path, ".test_write")
                with open(test_file, "w") as f:
                    f.write("test")
                os.remove(test_file)
                self.logger.info("Local setup validation successful")
                return True
            else:
                # For S3 mode, check bucket access
                if self._check_s3_bucket_access():
                    self.logger.info("S3 setup validation successful")
                    return True
                else:
                    self.logger.error("S3 setup validation failed")
                    return False
        except Exception as e:
            self.logger.error(f"Setup validation failed: {str(e)}")
            return False

    def _validate_dataframe(self, df: pd.DataFrame, metric_name: str) -> None:
        """
        Validate that the dataframe is not empty and has data.

        Args:
            df: DataFrame to validate
            metric_name: Name of the metric for error messages
        """
        if df is None:
            raise ValueError(f"DataFrame for {metric_name} is None")
        if df.empty:
            raise ValueError(f"DataFrame for {metric_name} is empty")

    def _save_dataframe_to_s3(
        self, df: pd.DataFrame, filename: str, metric_type: str
    ) -> None:
        """
        Save a DataFrame to S3.

        Args:
            df: DataFrame to save
            filename: Name of the file
            metric_type: Type of metric for logging
        """
        try:
            s3_file = S3File(
                bucket_name=GOLD_DATA_BUCKET,
                base_filename=f"{self.s3_path}/{filename.replace('.parquet', '')}",
                format="parquet",
            )
            self.s3_gateway.export_data(df, s3_file)
            self.logger.info(
                f"Successfully saved {metric_type} to S3: {s3_file.full_path}"
            )
        except Exception as e:
            self.logger.error(f"Failed to save {metric_type} to S3: {str(e)}")
            raise

    def _save_dataframe_to_local(
        self, df: pd.DataFrame, filename: str, metric_type: str
    ) -> None:
        """
        Save a DataFrame to local storage.

        Args:
            df: DataFrame to save
            filename: Name of the file
            metric_type: Type of metric for logging
        """
        try:
            self._ensure_local_directory_exists()
            file_path = os.path.join(self.local_path, filename)
            df.to_parquet(file_path, index=False)
            self.logger.info(f"Successfully saved {metric_type} to local: {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to save {metric_type} to local: {str(e)}")
            raise

    # Asset-level metrics methods
    def save_lender_asset_metrics_to_s3(
        self, metrics_sector_ctr: pd.DataFrame, metrics_portfolio: pd.DataFrame
    ) -> None:
        """
        Save asset-level metrics to S3.

        Args:
            metrics_sector_ctr: Sector/country/response ID level metrics
            metrics_portfolio: Portfolio level metrics
        """
        self._validate_dataframe(metrics_sector_ctr, "metrics_sector_ctr")
        self._validate_dataframe(metrics_portfolio, "metrics_portfolio")

        self._save_dataframe_to_s3(
            metrics_sector_ctr,
            self.ASSET_METRICS_FILES["metrics_sector_ctr"],
            "asset metrics (sector/ctr)",
        )
        self._save_dataframe_to_s3(
            metrics_portfolio,
            self.ASSET_METRICS_FILES["metrics_portfolio"],
            "asset metrics (portfolio)",
        )

    def save_lender_asset_metrics_to_local(
        self, metrics_sector_ctr: pd.DataFrame, metrics_portfolio: pd.DataFrame
    ) -> None:
        """
        Save asset-level metrics to local storage.

        Args:
            metrics_sector_ctr: Sector/country/response ID level metrics
            metrics_portfolio: Portfolio level metrics
        """
        self._validate_dataframe(metrics_sector_ctr, "metrics_sector_ctr")
        self._validate_dataframe(metrics_portfolio, "metrics_portfolio")

        self._save_dataframe_to_local(
            metrics_sector_ctr,
            self.ASSET_METRICS_FILES["metrics_sector_ctr"],
            "asset metrics (sector/ctr)",
        )
        self._save_dataframe_to_local(
            metrics_portfolio,
            self.ASSET_METRICS_FILES["metrics_portfolio"],
            "asset metrics (portfolio)",
        )

    # Building certification metrics methods
    def save_lender_bc_metrics_to_s3(
        self, bc_metrics: Tuple[pd.DataFrame, ...]
    ) -> None:
        """
        Save building certification metrics to S3.

        Args:
            bc_metrics: Tuple of 6 DataFrames containing BC metrics at different aggregation levels
        """
        if len(bc_metrics) != 6:
            raise ValueError(f"Expected 6 BC metrics DataFrames, got {len(bc_metrics)}")

        for i, (key, filename) in enumerate(self.BC_METRICS_FILES.items()):
            self._validate_dataframe(bc_metrics[i], f"BC metrics - {key}")
            self._save_dataframe_to_s3(bc_metrics[i], filename, f"BC metrics ({key})")

    def save_lender_bc_metrics_to_local(
        self, bc_metrics: Tuple[pd.DataFrame, ...]
    ) -> None:
        """
        Save building certification metrics to local storage.

        Args:
            bc_metrics: Tuple of 6 DataFrames containing BC metrics at different aggregation levels
        """
        if len(bc_metrics) != 6:
            raise ValueError(f"Expected 6 BC metrics DataFrames, got {len(bc_metrics)}")

        for i, (key, filename) in enumerate(self.BC_METRICS_FILES.items()):
            self._validate_dataframe(bc_metrics[i], f"BC metrics - {key}")
            self._save_dataframe_to_local(
                bc_metrics[i], filename, f"BC metrics ({key})"
            )

    # Energy rating metrics methods
    def save_lender_er_metrics_to_s3(
        self, er_metrics: Tuple[pd.DataFrame, ...]
    ) -> None:
        """
        Save energy rating metrics to S3.

        Args:
            er_metrics: Tuple of 4 DataFrames containing ER metrics at different aggregation levels
        """
        if len(er_metrics) != 4:
            raise ValueError(f"Expected 4 ER metrics DataFrames, got {len(er_metrics)}")

        for i, (key, filename) in enumerate(self.ER_METRICS_FILES.items()):
            self._validate_dataframe(er_metrics[i], f"ER metrics - {key}")
            self._save_dataframe_to_s3(er_metrics[i], filename, f"ER metrics ({key})")

    def save_lender_er_metrics_to_local(
        self, er_metrics: Tuple[pd.DataFrame, ...]
    ) -> None:
        """
        Save energy rating metrics to local storage.

        Args:
            er_metrics: Tuple of 4 DataFrames containing ER metrics at different aggregation levels
        """
        if len(er_metrics) != 4:
            raise ValueError(f"Expected 4 ER metrics DataFrames, got {len(er_metrics)}")

        for i, (key, filename) in enumerate(self.ER_METRICS_FILES.items()):
            self._validate_dataframe(er_metrics[i], f"ER metrics - {key}")
            self._save_dataframe_to_local(
                er_metrics[i], filename, f"ER metrics ({key})"
            )

    # Convenience methods for writing all metrics
    def write_all_lender_metrics(
        self,
        metrics_sector_ctr: pd.DataFrame,
        metrics_portfolio: pd.DataFrame,
        bc_metrics: Tuple[pd.DataFrame, ...],
        er_metrics: Tuple[pd.DataFrame, ...],
        write_local: bool = False,
    ) -> None:
        """
        Write all lender metrics (asset, BC, ER) to either S3 or local storage.

        Args:
            metrics_sector_ctr: Sector/country/response ID level metrics
            metrics_portfolio: Portfolio level metrics
            bc_metrics: Tuple of 6 DataFrames containing BC metrics
            er_metrics: Tuple of 4 DataFrames containing ER metrics
            write_local: Whether to save locally (True) or to S3 (False)
        """
        self.logger.info(
            f"Starting to write all lender metrics to {'local storage' if write_local else 'S3'}"
        )

        try:
            if write_local:
                self.save_lender_asset_metrics_to_local(
                    metrics_sector_ctr, metrics_portfolio
                )
                self.save_lender_bc_metrics_to_local(bc_metrics)
                self.save_lender_er_metrics_to_local(er_metrics)
            else:
                self.save_lender_asset_metrics_to_s3(
                    metrics_sector_ctr, metrics_portfolio
                )
                self.save_lender_bc_metrics_to_s3(bc_metrics)
                self.save_lender_er_metrics_to_s3(er_metrics)

            self.logger.info("Successfully wrote all lender metrics")
        except Exception as e:
            self.logger.error(f"Failed to write lender metrics: {str(e)}")
            raise

    def write_lender_asset_metrics(
        self,
        metrics_sector_ctr: pd.DataFrame,
        metrics_portfolio: pd.DataFrame,
        write_local: bool = False,
    ) -> None:
        """
        Write only asset-level lender metrics.

        Args:
            metrics_sector_ctr: Sector/country/response ID level metrics
            metrics_portfolio: Portfolio level metrics
            write_local: Whether to save locally (True) or to S3 (False)
        """
        if write_local:
            self.save_lender_asset_metrics_to_local(
                metrics_sector_ctr, metrics_portfolio
            )
        else:
            self.save_lender_asset_metrics_to_s3(metrics_sector_ctr, metrics_portfolio)
